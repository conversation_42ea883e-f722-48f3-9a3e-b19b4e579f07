import { FormatS3ImgUrl } from "@/utils";
import { useState } from "react";
import { Button, Dropdown, Image } from "react-bootstrap";

const FilterByInterest = ({
  interestData,
  selected,
  setSelected,
  toggleSelect,
  onSubmit,
}: any) => {
  const [show, setShow] = useState(false);

  const onToggle = () => setShow(!show);

  const onClickSearch = () => {
    setShow(false);
    onSubmit(selected);
  };

  const isSelected = (item) => selected.find((i) => i?.value === item?.value);

  return (
    <Dropdown show={show} onToggle={onToggle}>
      <Dropdown.Toggle
        variant="light"
        id="interest-dropdown"
        className="custom-dropdown interest"
      >
        Interests
      </Dropdown.Toggle>
      <Dropdown.Menu className="p-3 custom-interest-menu">
        <div className="d-flex flex-wrap gap-2 custom-interest-menu-data">
          {interestData.map((item: any) => (
            <div key={item.value} className="flex-1">
              <div
                className={`interest-card position-relative ${isSelected(item) ? "selected" : ""}`}
                onClick={() => toggleSelect(item)}
              >
                <Image
                  src={FormatS3ImgUrl(item.image)}
                  rounded
                  className="w-100 interest-img h-100 object-fit-cover"
                />
                <div className="label-overlay">{item.label}</div>
                <div className="checkmark"></div>
              </div>
            </div>
          ))}
        </div>
        <div className="d-flex align-items-center gap-3 mt-3 justify-content-end">
          <Button className="clear-filter-btn" onClick={() => setSelected([])}>
            Clear Filter
          </Button>
          <Button variant="primary" onClick={onClickSearch}>
            Search
          </Button>
        </div>
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default FilterByInterest;
