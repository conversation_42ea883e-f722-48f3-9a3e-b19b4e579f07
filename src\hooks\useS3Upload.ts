import { useGetS3PresignedUrlMutation, useUploadFileToS3Mutation } from "@/api/utils.api";
import { validateImageFile } from "@/utils/imageValidation";
import { useState } from "react";

interface UploadOptions {
  location?: string;
}

interface UploadResult {
  success: boolean;
  filename?: string;
  error?: string;
}

export const useS3Upload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const { mutateAsync: getPresignedUrl } = useGetS3PresignedUrlMutation();
  const { mutateAsync: uploadFileToS3 } = useUploadFileToS3Mutation();

  const uploadFile = async (
    file: File | null,
    options: UploadOptions = {}
  ): Promise<UploadResult> => {
    const { location = "users" } = options;
    if (!file) {
      return { success: false, error: "No file selected" };
    }

    // Validate file
    const error = validateImageFile(file);
    if (error) {
      return { success: false, error };
    }

    setIsUploading(true);

    try {
      const ext = file.name.split(".").pop()?.toLowerCase() || "jpg";
      const presignedRes: any = await getPresignedUrl({
        location,
        type: ext,
        count: 1,
      });

      const fileData = presignedRes?.data?.files?.[0];
      if (!fileData) {
        return { success: false, error: "Failed to get S3 upload URL." };
      }

      await uploadFileToS3({
        url: fileData.url,
        file,
        contentType: file.type || "application/octet-stream",
      });

      return {
        success: true,
        filename: fileData.filename,
      };
    } catch (err: any) {
      console.error("S3 upload error:", err);
      return {
        success: false,
        error: err?.message || "Failed to upload file to S3",
      };
    } finally {
      setIsUploading(false);
    }
  };

  return {
    uploadFile,
    isUploading,
  };
}; 