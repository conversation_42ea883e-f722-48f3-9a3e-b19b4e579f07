import React from "react";
import ReactSelect from "react-select";

interface MemoizedSelectProps {
  options: Array<{ value: any; label: string }>;
  value: any;
  onChange: (option: any, isMulti?: boolean) => void;
  name?: string;
  isSearchable?: boolean;
  placeholder?: string;
  classNamePrefix?: string;
  isMulti?: boolean;
}

const MemoizedSelect: React.FC<MemoizedSelectProps> = React.memo(
  ({ options, value, onChange, isMulti, ...props }) => (
    <ReactSelect
      options={options}
      value={
        isMulti
          ? Array.isArray(value)
            ? options.filter((o: { value: any }) => value.includes(o.value))
            : []
          : options.find((o: { value: any }) => o.value === value) || null
      }
      onChange={(option) => onChange(option, isMulti)}
      isMulti={isMulti}
      {...props}
    />
  )
);

export default MemoizedSelect;
