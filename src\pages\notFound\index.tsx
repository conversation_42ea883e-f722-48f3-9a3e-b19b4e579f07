import { useTranslation } from '@/hooks/useTranslation';
import { ROUTE_PATH } from "@/routes";
import { IMAGE_PATHS } from "@/utils/image-path";
import parse from 'html-react-parser';
import { Form, Image } from "react-bootstrap";
import { Link } from "react-router-dom";
import "./styles.scss";

const NotFound = () => {
  const { t } = useTranslation();
  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column notfound-form"
      style={{ gap: "30px" }}
    >
      <div
        className="d-flex flex-column align-items-center"
        style={{ gap: "23px" }}
      >
        <div className="notfound-icon mb-2">
          <Image src={IMAGE_PATHS.BrokeHeart} alt="broke-heart-icon" />
        </div>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          {t('notFound.title')}
        </h1>
        <p className="mb-0 auth-form-description font-gray text-center">
          {parse(t('notFound.description'))}
        </p>
      </div>
      <div className="website-form">
        <Form className="d-flex flex-column" style={{ gap: "30px" }}>
          <div
            className="action-btns d-flex flex-column"
            style={{ gap: "30px" }}
          >
            <Link
              to={ROUTE_PATH.HOME}
              className="d-flex justify-content-center align-items-center text-decoration-none submit-btn position-relative w-100 border-brown bg-transparent text-uppercase font-primary"
            >
              {t('notFound.goHome')}
            </Link>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default NotFound;
