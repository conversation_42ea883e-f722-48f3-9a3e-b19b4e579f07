import { useUpdateProfileMutation } from "@/api/user.api";
import { useMaster } from "@/api/utils.api";
import AboutMeSection from "@/components/profileManagement/AboutMeSection";
import AppearanceSection from "@/components/profileManagement/AppearanceSection";
import BasicInfoSection from "@/components/profileManagement/BasicInfoSection";
import FamilySection from "@/components/profileManagement/FamilySection";
import LifestyleSection from "@/components/profileManagement/LifestyleSection";
import LocationSection from "@/components/profileManagement/LocationSection";
import MemoSection from "@/components/profileManagement/MemoSection";
import PersonalAttributesSection from "@/components/profileManagement/PersonalAttributesSection";
import ProfilePictureSection from "@/components/profileManagement/ProfilePictureSection";
import { ROUTE_PATH } from "@/routes";
import { setAuthLoading } from "@/stores";
import useUserStore from "@/stores/user";
import { useTranslation } from "@/hooks/useTranslation";
import { useFormik } from "formik";
import React, { useCallback, useMemo, useEffect } from "react";
import { Accordion, Button, Container, Form } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import "./styles.scss";

// Add this type for the form values
export type ProfileFormValues = {
  name: string | undefined;
  email: string | undefined;
  gender: string | undefined;
  seekingFor: string | undefined;
  dob: string | undefined;
  appearance: string | undefined;
  hairColor: string | undefined;
  eyeColor: string | undefined;
  bodyType: string | undefined;
  bestFeature: string | undefined;
  bodyArt: string | undefined;
  height: string | undefined;
  weight: string | undefined;
  relationshipStatus: string | undefined;
  starSign: string | undefined;
  smokingHabit: string | undefined;
  drinkingHabit: string | undefined;
  sexualOrientation: string | undefined;
  personality: string | undefined;
  ethnicity: string | undefined;
  religion: string | undefined;
  interest: string[] | undefined;
  city: string | undefined;
  kids: string | undefined;
  aboutMe: string | undefined;
  avatar: string | undefined;
};

const ProfileManagement: React.FC = () => {
  const { t } = useTranslation();
  const userData = useUserStore((state) => state.userInfo.user);
  const { mutateAsync: updateProfile } = useUpdateProfileMutation();
  const { data: master = {}, isLoading: isMasterLoading } = useMaster();
  const navigate = useNavigate();

  useEffect(() => {
    setAuthLoading(false);
  }, []);

  // Memoize all dropdown options
  const appearanceOptions = useMemo(
    () =>
      (master.appearance || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.appearance]
  );
  const relationshipStatusOptions = useMemo(
    () =>
      (master.relationship_status || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.relationship_status]
  );
  const personalityOptions = useMemo(
    () =>
      (master.personality || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.personality]
  );
  const eyeColorOptions = useMemo(
    () =>
      (master.eye_color || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.eye_color]
  );
  const bodyTypeOptions = useMemo(
    () =>
      (master.body_type || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.body_type]
  );
  const hairColorOptions = useMemo(
    () =>
      (master.hair_color || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.hair_color]
  );
  const smokingHabitOptions = useMemo(
    () =>
      (master.smoking_habits || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.smoking_habits]
  );
  const drinkingHabitOptions = useMemo(
    () =>
      (master.drinking_habits || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.drinking_habits]
  );
  const bestFeatureOptions = useMemo(
    () =>
      (master.best_feature || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.best_feature]
  );
  const bodyArtOptions = useMemo(
    () =>
      (master.body_art || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.body_art]
  );
  const sexualOrientationOptions = useMemo(
    () =>
      (master.sexual_orientation || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.sexual_orientation]
  );
  const ethnicityOptions = useMemo(
    () =>
      (master.ethnicity || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.ethnicity]
  );
  const starSignOptions = useMemo(
    () =>
      (master.star_sign || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.star_sign]
  );
  const seekingForOptions = useMemo(
    () =>
      (master.seeking_for || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.seeking_for]
  );

  const genderOptions = useMemo(
    () =>
      (master.gender || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.gender]
  );

  const religionOptions = useMemo(
    () =>
      (master.religion || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.religion]
  );
  const interestsOptions = useMemo(
    () =>
      (master.interest || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.interest]
  );

  const initialValues = {
    name: userData?.name || undefined,
    username: userData?.username || undefined,
    email: userData?.email || undefined,
    gender: (userData as any)?.customer_profile?.gender?.id || undefined,
    seekingFor:
      (userData as any)?.customer_profile?.seekingFor?.id || undefined,
    dob: (userData as any)?.customer_profile?.dob || undefined,
    appearance:
      (userData as any)?.customer_profile?.appearance?.id || undefined,
    hairColor: (userData as any)?.customer_profile?.hairColor?.id || undefined,
    eyeColor: (userData as any)?.customer_profile?.eyeColor?.id || undefined,
    bodyType: (userData as any)?.customer_profile?.bodyType?.id || undefined,
    bestFeature:
      (userData as any)?.customer_profile?.bestFeature?.id || undefined,
    bodyArt: (userData as any)?.customer_profile?.bodyArt?.id || undefined,
    height: (userData as any)?.customer_profile?.height || undefined,
    weight: (userData as any)?.customer_profile?.weight || undefined,
    relationshipStatus:
      (userData as any)?.customer_profile?.relationshipStatus?.id || undefined,
    starSign: (userData as any)?.customer_profile?.starSign?.id || undefined,
    smokingHabit:
      (userData as any)?.customer_profile?.smokingHabit?.id || undefined,
    drinkingHabit:
      (userData as any)?.customer_profile?.drinkingHabit?.id || undefined,
    sexualOrientation:
      (userData as any)?.customer_profile?.sexualOrientation?.id || undefined,
    personality:
      (userData as any)?.customer_profile?.personality?.id || undefined,
    ethnicity: (userData as any)?.customer_profile?.ethnicity?.id || undefined,
    religion: (userData as any)?.customer_profile?.religion?.id || undefined,
    interest: (() => {
      const interestData = (userData as any)?.customer_profile?.interest;
      if (Array.isArray(interestData)) {
        return interestData.map((item: any) => item.id || item);
      } else if (interestData?.id) {
        return [interestData.id];
      } else if (interestData) {
        return [interestData];
      }
      return [];
    })(),
    city: (userData as any)?.city || undefined,
    kids: (userData as any)?.customer_profile?.kids || undefined,
    aboutMe: (userData as any)?.customer_profile?.aboutMe || undefined,
    avatar: (userData as any)?.avatar || undefined,
  };

  const formik = useFormik<ProfileFormValues>({
    initialValues,
    enableReinitialize: true,
    validateOnChange: false,
    validateOnBlur: true,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const payload = { ...values };
        const result: any = await updateProfile(payload);
        if (result?.success) {
          navigate(ROUTE_PATH.HOME);
        }
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleSelectChange = useCallback(
    (field: string) => (option: any, isMulti?: boolean) => {
      if (isMulti) {
        formik.setFieldValue(field, option?.map((o: any) => o.value) || []);
        return;
      }
      formik.setFieldValue(field, option?.value || "");
    },
    [formik]
  );

  const handleBlurUncontrolled = useCallback(
    (field: string) =>
      (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        formik.setFieldValue(
          field,
          e.target.type === "number" ? Number(e.target.value) : e.target.value
        );
      },
    [formik]
  );

  console.log('ProfileManagement formik.values:', formik.values);

  return (
    <div className="profile-management">
      <Container>
        <Form noValidate onSubmit={formik.handleSubmit}>
          <ProfilePictureSection />
          <Accordion className="d-flex flex-column" defaultActiveKey={["0"]}>
            <MemoSection>
              <BasicInfoSection
                formik={formik}
                handleBlurUncontrolled={handleBlurUncontrolled}
                seekingForOptions={seekingForOptions}
                genderOptions={genderOptions}
              />
            </MemoSection>
            <MemoSection>
              <AppearanceSection
                formik={formik}
                handleBlurUncontrolled={handleBlurUncontrolled}
                handleSelectChange={handleSelectChange}
                appearanceOptions={appearanceOptions}
                hairColorOptions={hairColorOptions}
                eyeColorOptions={eyeColorOptions}
                bodyTypeOptions={bodyTypeOptions}
                bestFeatureOptions={bestFeatureOptions}
                bodyArtOptions={bodyArtOptions}
                isMasterLoading={isMasterLoading}
              />
            </MemoSection>
            <MemoSection>
              <LifestyleSection
                formik={formik}
                handleSelectChange={handleSelectChange}
                relationshipStatusOptions={relationshipStatusOptions}
                starSignOptions={starSignOptions}
                smokingHabitOptions={smokingHabitOptions}
                drinkingHabitOptions={drinkingHabitOptions}
                sexualOrientationOptions={sexualOrientationOptions}
                isMasterLoading={isMasterLoading}
              />
            </MemoSection>
            <MemoSection>
              <PersonalAttributesSection
                formik={formik}
                handleSelectChange={handleSelectChange}
                personalityOptions={personalityOptions}
                ethnicityOptions={ethnicityOptions}
                religionOptions={religionOptions}
                interestsOptions={interestsOptions}
                isMasterLoading={isMasterLoading}
              />
            </MemoSection>
            <MemoSection>
              <LocationSection
                formik={formik}
                handleBlurUncontrolled={handleBlurUncontrolled}
              />
            </MemoSection>
            <MemoSection>
              <FamilySection
                formik={formik}
                handleBlurUncontrolled={handleBlurUncontrolled}
              />
            </MemoSection>
            <MemoSection>
              <AboutMeSection
                formik={formik}
                handleBlurUncontrolled={handleBlurUncontrolled}
              />
            </MemoSection>
          </Accordion>
          <div className="d-flex gap-3 gap-md-4 mt-4 mt-md-5">
            <Button type="submit" disabled={formik.isSubmitting}>
              {t('profileManagement.submit')}
            </Button>
            <Button className="bordered-btn" type="button">
              {t('profileManagement.cancel')}
            </Button>
          </div>
        </Form>
      </Container>
    </div>
  );
};

export default ProfileManagement;
