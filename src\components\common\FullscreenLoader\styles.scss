@use "@/variables" as *;

.fullscreen-loader {
  animation: fadeIn 0.2s ease-in-out;
  background: radial-gradient(
    88.98% 88.98% at 50% 50%,
    #ffeed6 0%,
    #dbb585 100%
  );

  .spinner-border {
    border-width: 3px;
  }

  p {
    font-size: 16px;
    letter-spacing: 0.5px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.loader-enter {
  opacity: 0;
}

.loader-enter-active {
  opacity: 1;
  transition: opacity 200ms ease-in;
}

.loader-exit {
  opacity: 1;
}

.loader-exit-active {
  opacity: 0;
  transition: opacity 200ms ease-out;
}
