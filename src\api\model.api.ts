import { API_ENDPOINTS } from "@/globals";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "./apiClient";
import { IDType } from "@/types";

export const useModels = ({ params = {} }) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_MODELS, {
        params,
      });
      return response;
    },
    queryKey: ["models", ...Object.values(params)],
  });

export const useFlirtMessages = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_FLIRT_MESSAGES);
      return response;
    },
    queryKey: ["flirt-messages"],
  });

export const useBotMessages = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_BOT_MESSAGES);
      return response;
    },
    queryKey: ["bot-messages"],
  });

export const useModelDetails = (model_id: IDType) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_MODEL_DETAILS(model_id)
      );
      return response;
    },
    queryKey: ["model-details", model_id],
  });

export const useToggleFavorite = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.post(API_ENDPOINTS.TOGGLE_FAVORITE, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["models"] });
    },
  });
};
