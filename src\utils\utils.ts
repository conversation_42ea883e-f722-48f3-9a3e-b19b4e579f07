export const getAppOriginURL = () => {
    return `${window.location.origin}${import.meta.env.VITE_BASE_URL?.length > 1 ? import.meta.env.VITE_BASE_URL : ""}`;
};

export const calculateAgeByDOB = (dob: string | undefined) => {
    if (!dob) return null;

    const birthDate = new Date(dob);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();

    const monthDiff = today.getMonth() - birthDate.getMonth();
    const dayDiff = today.getDate() - birthDate.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age--;
    }

    return `, ${age}`;
};


export const stringToArray = (value: string): string[] => {
    return value ? value.split(",").filter(Boolean) : [];
};

export const arrayToString = (array: string[]): string => {
    return array.join(",");
};

export const toggleItemInArray = (array: string[], item: string): string[] => {
    return array.includes(item)
        ? array.filter((i) => i !== item)
        : [...array, item];
};

export const removeItemFromArray = (array: string[], item: string): string[] => {
    return array.filter((i) => i !== item);
};

export const FormatS3ImgUrl = (filename: string) => {
    return `${import.meta.env.VITE_S3_BASE_URL}${filename}`
}

export const FormatLocation = (country?: string, city?: string): string => {
    if (country && city) return `${country}, ${city}`;
    if (country) return country;
    if (city) return city;
    return "";
};