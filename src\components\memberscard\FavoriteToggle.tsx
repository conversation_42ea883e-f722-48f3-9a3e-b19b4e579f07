import { useEffect, useState } from "react";
import { Heart } from "iconsax-react";
import { useToggleFavorite } from "@/api";
import { Spinner } from "react-bootstrap";

interface FavoriteToggleProps {
  modelId: number;
  initialFavorite: boolean;
}

const FavoriteToggle: React.FC<FavoriteToggleProps> = ({
  modelId,
  initialFavorite,
}) => {
  const [isFavorite, setIsFavorite] = useState(initialFavorite);
  const { mutateAsync: toggleFavorite, isPending } = useToggleFavorite();

  const handleToggle = async (e: any) => {
    e.stopPropagation();
    try {
      const res: any = await toggleFavorite({ modelId });
      if (res?.success) {
        setIsFavorite(!isFavorite);
      }
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    setIsFavorite(initialFavorite);
  }, [initialFavorite]);

  return (
    <div
      className="favorite-icon"
      style={{ cursor: "pointer" }}
      onClick={handleToggle}
    >
      {isPending ? (
        <Spinner animation="border" size="sm" />
      ) : (
        <Heart
          size="24"
          color={isFavorite ? "#ff0000" : "#999"}
          variant={isFavorite ? "Bold" : "Outline"}
        />
      )}
    </div>
  );
};

export default FavoriteToggle;
