import defaultProfile from "@/assets/images/user.png";
import useChatStore from "@/stores/useChatStore";
import { ModelInterface } from "@/types";
import { calculateAgeByDOB, FormatLocation, FormatS3ImgUrl } from "@/utils";
import { Sms } from "iconsax-react";
import React, { useState } from "react";
import { Button, Image } from "react-bootstrap";
import FlirtPanel from "../flirtpanel/FlirtPanel";
import FavoriteToggle from "./FavoriteToggle";
import MemberProfilePanel from "./memberProfilePanel";
import "./styles.scss";

const MembersCard: React.FC<ModelInterface> = (modelProps) => {
  const [flirtShow, setFlirtShow] = useState<boolean>(false);
  const [selectedMemberId, setSelectedMemberId] = useState<number | null>(null);
  const { setActiveChatUser } = useChatStore();

  const {
    avatar,
    username,
    city,
    model_profile,
    modelId: memberId,
    isFavorite,
    id,
  } = modelProps || {};
  const { dob } = model_profile || {};

  const handleClose = () => setSelectedMemberId(null);

  const handleShow = (e: any) => {
    e.stopPropagation();
    setSelectedMemberId(id);
  };
  const handleFlirtShow = (e: any) => {
    e.stopPropagation();
    setFlirtShow(true);
  };
  const onHide = () => {
    setFlirtShow(false);
  };

  const handleChatShow = (e: any) => {
    e.stopPropagation();
    setActiveChatUser({ id: userId, name, location });
  };

  return (
    <>
      <div className="member-card overflow-hidden" onClick={handleShow}>
        <div className="position-relative w-100">
          <Image
            src={avatar ? FormatS3ImgUrl(avatar) : defaultProfile}
            className="member-img w-100"
          />
          <FavoriteToggle modelId={memberId} initialFavorite={isFavorite} />
        </div>
        <div className="p-3 d-flex flex-column gap-3">
          <div className="d-flex justify-content-between gap-3">
            <div className="d-flex flex-column gap-1">
              <h3 className="mb-0 name">
                {username}
                {calculateAgeByDOB(dob)}
              </h3>
              <p className="mb-0 location">{FormatLocation(undefined, city)}</p>
            </div>
            <Button variant="outline-warning" className="email-btn">
              <Sms size="32" color="#f68507" variant="Bold" />
            </Button>
          </div>
          <div className="d-flex gap-2">
            <Button
              variant="outline-warning"
              className="w-100 first-btn"
              onClick={handleChatShow}
            >
              Chat
            </Button>
            <Button
              variant="gradient-purple"
              className="w-100 second-btn"
              onClick={handleFlirtShow}
            >
              Flirt
            </Button>
          </div>
        </div>
      </div>
      {selectedMemberId && (
        <MemberProfilePanel
          show={!!selectedMemberId}
          onHide={handleClose}
          memberId={selectedMemberId}
          handleFlirtShow={handleFlirtShow}
        />
      )}
      {flirtShow && <FlirtPanel show={flirtShow} onHide={onHide} />}
    </>
  );
};

export default MembersCard;
