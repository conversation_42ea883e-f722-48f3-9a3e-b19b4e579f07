import {
  useActivePackage,
  usePackages,
  usePurchasePackageMutation,
} from "@/api";
import PlanCard from "@/components/coincard/planCard";
import React, { useEffect, useState } from "react";
import { Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import "./styles.scss";
import { setUserDetails } from "@/stores";
import { useTranslation } from "@/hooks/useTranslation";
import useUserStore from "@/stores/user";

const CoinPurchase: React.FC = () => {
  const { t } = useTranslation();
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const { data, isLoading, isError } = usePackages();
  const { data: { data: packageData = {} } = {} } = useActivePackage();
  const { package: activePackage } = packageData || {};

  const { mutate: purchasePackage } = usePurchasePackageMutation();
  const userData = useUserStore((state) => state.userInfo.user);

  const plans = data?.data?.packages || [];

  const handleBuy = (index: number) => {
    if (!plans[index]) return;
    setActiveIndex(index);
    const plan = plans[index];
    purchasePackage(
      { packageId: plan.id },
      {
        onSuccess: (res: any) => {
          setUserDetails({
            ...userData,
            coins: res?.data?.coins,
          });
          toast.success(res?.message || t('coin.purchaseSuccess'));
        },
        onError: (err: any) => {
          toast.error(err?.response?.data?.message || t('coin.purchaseFailed'));
        },
      }
    );
  };

  const isActivePackage = (id: any) => {
    return activePackage?.id === id;
  };
  return (
    <div className="coin-purchage-section inner-height">
      <Container>
        <div className="d-flex flex-column gap-2 coin-purchage-section-content text-center">
          <h2 className="mb-0 title">{t('coin.title')}</h2>
          <p className="mb-0 description">
            {t('coin.description')}
          </p>
        </div>
        {isLoading ? (
          <div className="text-center my-5">
            <Spinner animation="border" />
          </div>
        ) : isError ? (
          <div className="text-danger text-center my-5">
            {t('coin.failedToLoad')}
          </div>
        ) : (
          <Row>
            {plans.map((plan: any, index: number) => (
              <PlanCard
                key={plan.id || index}
                index={index}
                title={plan?.packageName}
                coins={plan.coins}
                originalPrice={parseFloat(plan.amount)}
                currentPrice={parseFloat(plan.discountAmount)}
                isActive={isActivePackage(plan?.id)}
                onBuy={handleBuy}
              />
            ))}
          </Row>
        )}
      </Container>
    </div>
  );
};

export default CoinPurchase;
