import AuthLayout from "@/components/layouts/authLayout";
import FullscreenLoader from "@/components/common/FullscreenLoader";
import { NotFound } from "@/pages";
import { PrivateRoutes, PublicRoutes } from "@/routes";
import useUserStore from "@/stores/user";
import { Route, Routes } from "react-router-dom";
import { useState, useEffect } from "react";

export const RoutingProvider = () => {
  const access_token = useUserStore((state) => state.userInfo.access_token);
  const isAuthLoading = useUserStore((state) => state.isAuthLoading);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previousTokenState, setPreviousTokenState] = useState<string | null>(
    access_token
  );

  useEffect(() => {
    if (previousTokenState !== access_token) {
      if (!previousTokenState && access_token) {
        setIsTransitioning(true);
        const timer = setTimeout(() => {
          setIsTransitioning(false);
        }, 300);
        return () => clearTimeout(timer);
      }
      setPreviousTokenState(access_token);
    }
  }, [access_token, previousTokenState]);

  if (isTransitioning || isAuthLoading) {
    return <FullscreenLoader message="Redirecting..." />;
  }

  return (
    <Routes>
      {access_token ? PrivateRoutes() : PublicRoutes()}
      <Route element={<AuthLayout />}>
        <Route path="*" element={<NotFound />} />
      </Route>
    </Routes>
  );
};
