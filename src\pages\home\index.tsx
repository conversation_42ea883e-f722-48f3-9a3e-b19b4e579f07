import { useModels } from "@/api";
import ChatPopup from "@/components/chatbot/ChatPopup";
import Header from "@/components/common/Header";
import SearchBar from "@/components/common/Search";
import MembersCard from "@/components/memberscard/MembersCard";
import { useAutoScroll, useFormattedFilters } from "@/hooks";
import { useTranslation } from "@/hooks/useTranslation";
import { ModelInterface } from "@/types";
import { useState } from "react";
import { Container, Spinner } from "react-bootstrap";
import "./styles.scss";

const Home = () => {
  const { t } = useTranslation();
  const initialFilter = {
    name: undefined,
    relationshipStatusId: undefined,
    hairColorId: undefined,
    bestFeatureId: undefined,
    eyeColorId: undefined,
    personalityId: undefined,
    appearanceId: undefined,
    bodyTypeId: undefined,
    smokingHabitId: undefined,
    drinkingHabitId: undefined,
    interestIds: undefined,
    ageRange: undefined,
    distance: undefined,
    withPhotos: false,
  };

  const [filters, setFilters] = useState(initialFilter);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
  });

  const formattedFilters = useFormattedFilters(filters);
  const [params, setParams] = useState(formattedFilters);

  const { data: { data: { models = [] } = {} } = {}, isLoading } = useModels({
    params: { ...params, ...pagination },
  });

  useAutoScroll();

  const onClearFilters = () => {
    setFilters(initialFilter);
    setParams(initialFilter);
  };

  const onSearchWithFilters = () => {
    setPagination({ page: 1, limit: 50 });
    setParams(formattedFilters);
  };

  return (
    <div>
      <Header />
      <div className="banner-img">
        <Container fluid>
          <div className="d-flex flex-column gap-2 mb-3">
            <h2 className="title mb-0">{t('home.title')}</h2>
            <p className="description mb-0">{t('home.description')}</p>
          </div>
          <SearchBar
            filters={filters}
            setFilters={setFilters}
            onClearFilters={onClearFilters}
            onSearchWithFilters={onSearchWithFilters}
            setParams={setParams}
          />
        </Container>
      </div>
      <div className="members-list">
        <Container fluid>
          {isLoading ? (
            <div className="d-flex justify-content-center align-items-center w-100">
              <Spinner />
            </div>
          ) : (
            <div className="d-flex flex-wrap members-list-content">
              {models.map((model: ModelInterface) => (
                <MembersCard key={model.id} {...model} />
              ))}
            </div>
          )}
        </Container>
      </div>
      <ChatPopup />
    </div>
  );
};

export default Home;
