import React, { useState } from "react";
import { Dropdown, Form } from "react-bootstrap";

export interface DropdownOption {
  label: string;
  value: string;
}

interface CustomDropdownProps {
  title?: string;
  options: DropdownOption[];
  selectedOptions: string[];
  onSelect: (selected: string[]) => void;
  multiSelect?: boolean;
  dropdownProps?: Partial<React.ComponentProps<typeof Dropdown>>;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  title = "Select",
  options,
  selectedOptions,
  onSelect,
  multiSelect = true,
  dropdownProps = {},
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleChange = (value: string) => {
    if (!onSelect) return;

    if (multiSelect) {
      const isSelected = selectedOptions.includes(value);
      const newSelected = isSelected
        ? selectedOptions.filter((v) => v !== value)
        : [...selectedOptions, value];
      onSelect(newSelected);
    } else {
      onSelect([value]);
      setShowDropdown(false);
    }
  };

  return (
    <Dropdown
      show={showDropdown}
      onToggle={() => setShowDropdown(!showDropdown)}
      {...dropdownProps}
    >
      <Dropdown.Toggle
        variant="light"
        id="custom-dropdown"
        className="custom-dropdown"
      >
        {title}
      </Dropdown.Toggle>

      <Dropdown.Menu>
        {options.map((option) => (
          <div
            key={option.value}
            className="dropdown-item d-flex align-items-center"
            onClick={(e) => {
              e.stopPropagation();
              handleChange(option.value);
            }}
            style={{ cursor: "pointer" }}
          >
            {multiSelect ? (
              <Form.Check
                type="checkbox"
                id={`dropdown-${option.value}`}
                label={option.label}
                checked={selectedOptions.includes(option.value)}
                onChange={() => {}}
                className="mb-0"
              />
            ) : (
              option.label
            )}
          </div>
        ))}
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default CustomDropdown;
