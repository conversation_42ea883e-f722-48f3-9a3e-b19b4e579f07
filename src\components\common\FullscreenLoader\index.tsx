import React from "react";
import { Spinner } from "react-bootstrap";
import "./styles.scss";

interface FullscreenLoaderProps {
  message?: string;
  variant?: "light" | "dark";
  size?: "sm" | "md" | "lg";
}

const FullscreenLoader: React.FC<FullscreenLoaderProps> = ({
  message = "Loading...",
  variant = "light",
  size = "md",
}) => {
  const spinnerSizes = {
    sm: { width: "2rem", height: "2rem" },
    md: { width: "3rem", height: "3rem" },
    lg: { width: "4rem", height: "4rem" },
  };

  const textColors = {
    light: "text-muted",
    dark: "text-light",
  };

  return (
    <div
      className="fullscreen-loader d-flex justify-content-center align-items-center position-fixed top-0 start-0 w-100 h-100"
      style={{
        zIndex: 9999,
        backdropFilter: "blur(2px)",
      }}
    >
      <div className="d-flex flex-column align-items-center gap-3">
        <Spinner animation="border" role="status" style={spinnerSizes[size]}>
          <span className="visually-hidden">{message}</span>
        </Spinner>
        <p className={`mb-0 fw-medium ${textColors[variant]}`}>{message}</p>
      </div>
    </div>
  );
};

export default FullscreenLoader;
