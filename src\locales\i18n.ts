import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import auth from '@/locales/auth.json';
import common from '@/locales/common.json';
import pages from '@/locales/pages.json';
import components from '@/locales/components.json';

// For now, language is hardcoded to 'en'.
const defaultLanguage = 'en';

const resources = {
  en: {
    translation: {
      ...auth,
      ...common,
      ...pages,
      ...components
    }
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: defaultLanguage,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false, // React already escapes
    },
  });

export default i18n;
