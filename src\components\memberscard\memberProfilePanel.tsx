import { useModelDetails } from "@/api";
import defaultProfile from "@/assets/images/user.png";
import { calculateAgeByDOB, FormatLocation, FormatS3ImgUrl } from "@/utils";
import { IMAGE_PATHS } from "@/utils/image-path";
import { Sms } from "iconsax-react";
import { Button, Col, Image, Offcanvas, Row, Spinner } from "react-bootstrap";
import FavoriteToggle from "./FavoriteToggle";

interface MemberProfilePanelProps {
  show: boolean;
  onHide: () => void;
  memberId: number;
  handleFlirtShow: any;
}

const MemberProfilePanel: React.FC<MemberProfilePanelProps> = ({
  show,
  onHide,
  memberId,
  handleFlirtShow,
}) => {
  const { data: { data: memberDetails = {} } = {}, isLoading } =
    useModelDetails(memberId);

  const {
    username,
    city,
    dob,
    height,
    weight,
    appearance,
    hairColor,
    eyeColor,
    bodyType,
    bodyArt,
    relationshipStatus,
    ethnicity,
    smokingHabit,
    drinkingHabit,
    gender,
    personality,
    seekingFor,
    modelGroup,
    aboutme,
    kids,
    avatar,
    isFavorite,
    modelId,
  } = memberDetails || {};

  const userAge = dob ? calculateAgeByDOB(dob) : "";

  return (
    <Offcanvas
      show={show}
      onHide={onHide}
      placement="end"
      className="user-profile-detail"
    >
      {isLoading ? (
        <div className="d-flex justify-content-center align-items-center h-100">
          <Spinner />
        </div>
      ) : (
        <>
          <Offcanvas.Header closeButton className="user-profile-detail-header">
            <Offcanvas.Title>
              <div className="d-flex flex-column gap-1">
                <div className="name">
                  {username}
                  {userAge}
                </div>
                <div className="location">
                  {FormatLocation(undefined, city)}
                </div>
              </div>
            </Offcanvas.Title>
          </Offcanvas.Header>

          <Offcanvas.Body className="p-4 d-flex flex-column gap-3">
            {/* Profile Image Section */}
            <div className="d-flex flex-column gap-2 img-section">
              <div className="position-relative w-100">
                <Image
                  src={avatar ? `${FormatS3ImgUrl(avatar)}` : defaultProfile}
                  className={`large-img w-100 ${avatar ? '' : 'object-fit-contain'}`}
                />
                <FavoriteToggle
                  modelId={modelId}
                  initialFavorite={isFavorite}
                />
              </div>
              <div className="d-flex gap-2 image-scroll">
                <Image
                  src={IMAGE_PATHS.UserDetailImg2}
                  className="small-img w-100"
                />
                <Image
                  src={IMAGE_PATHS.UserDetailImg3}
                  className="small-img w-100"
                />
                <Image
                  src={IMAGE_PATHS.UserDetailImg4}
                  className="small-img w-100"
                />
                <Image
                  src={IMAGE_PATHS.UserDetailImg5}
                  className="small-img w-100"
                />
              </div>
            </div>

            {/* Buttons */}
            <div className="d-flex justify-content-between btn-section">
              <Button variant="outline-warning" className="w-100 first-btn">
                Chat
              </Button>
              <Button
                variant="gradient-purple"
                className="w-100 second-btn"
                onClick={(e) => {
                  onHide();
                  handleFlirtShow(e);
                }}
              >
                Flirt
              </Button>
              <Button variant="outline-warning" className="email-btn">
                <Sms size="32" color="#f68507" variant="Bold" />
              </Button>
            </div>

            {/* About Me */}
            <div className="details-section">
              <h6 className="title">About Me</h6>
              <p className="description mb-0">{aboutme || "-"}</p>
            </div>

            {/* Personal Details */}
            <div className="details-section">
              <h6 className="title">Personal Details</h6>
              <Row>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Gender<span>{gender?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Relation Status<span>{relationshipStatus?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Personality<span>{personality?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Location<span>{city || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Kids<span>{kids || "-"}</span>
                </Col>
              </Row>
            </div>

            {/* Physical Attributes */}
            <div className="details-section">
              <h6 className="title">Physical Attributes</h6>
              <Row>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Hair Color<span>{hairColor?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Eye Color<span>{eyeColor?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Height<span>{height ? `${height} cm` : "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Weight<span>{weight ? `${weight} kg` : "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Appearance<span>{appearance?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Body Type<span>{bodyType?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Body Art<span>{bodyArt?.title || "-"}</span>
                </Col>
              </Row>
            </div>

            {/* Lifestyle */}
            <div className="details-section">
              <h6 className="title">Lifestyle</h6>
              <Row>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Smoking<span>{smokingHabit?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Drinking<span>{drinkingHabit?.title || "-"}</span>
                </Col>
              </Row>
            </div>

            {/* Other Information */}
            <div className="details-section">
              <h6 className="title">Other Information</h6>
              <Row>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Seeking For<span>{seekingFor?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Ethnicity<span>{ethnicity?.title || "-"}</span>
                </Col>
                <Col xs={6} className="d-flex flex-column details-section-info">
                  Model Group<span>{modelGroup?.title || "-"}</span>
                </Col>
              </Row>
            </div>
          </Offcanvas.Body>
        </>
      )}
    </Offcanvas>
  );
};

export default MemberProfilePanel;
