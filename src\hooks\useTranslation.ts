import { useTranslation as useI18NextTranslation } from 'react-i18next';
import { useLanguageStore } from '@/stores/language';
import i18n from '@/locales/i18n';
import { useEffect } from 'react';

export function useTranslation() {
  const language = useLanguageStore((state) => state.language);
  const { t, i18n: i18nInstance } = useI18NextTranslation();

  useEffect(() => {
    if (i18n.language !== language) {
      i18n.changeLanguage(language);
    }
  }, [language]);

  return { t, i18n: i18nInstance };
}
