import React from 'react';
import './styles.scss';
import { useTranslation } from '@/hooks/useTranslation';
import { Button, Container } from 'react-bootstrap';

const Flirts: React.FC = () => {
    const { t } = useTranslation();
    return (
        <div className='flirts-page'>
            <Container fluid>
                <div className='d-flex flex-column gap-3'>
                    <div className='d-flex aling-items-center gap-3'>
                        <Button className='second-btn'>
                            {t('flirts.received')}
                        </Button>
                        <Button className='first-btn'>
                            {t('flirts.sent')}
                        </Button>

                    </div>

                </div>
            </Container>
        </div>
    )
}

export default Flirts