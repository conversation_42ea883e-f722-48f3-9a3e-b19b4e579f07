@use '@/variables' as *;

.profile-management {
    min-height: calc(100dvh - 73px);
    padding: 80px 0;
    background: radial-gradient(50% 50% at 50% 50%, #FEEBD0 0%, #FFDEB7 100%);

    &-inner {
        gap: 20px;
    }

    .profile-box {
        padding: 50px;
        border-radius: 10px;
        background: $white-color;
        margin-bottom: 20px;

        .profile-upload {
            padding: 20px 0;

            .circle {
                width: 150px;
                height: 150px;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 50%;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    z-index: 5;
                }

                &.has-image:hover {
                    &::after {
                        opacity: 1;
                    }

                    .delete-icon {
                        opacity: 1;
                    }
                }

                .p-image {
                    position: absolute;
                    bottom: 10px;
                    right: 0;
                    background: $black-color;
                    border: 3px solid $white-color;
                    cursor: pointer;
                    width: 40px;
                    height: 40px;

                    .file-upload {
                        opacity: 0;
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 34px;
                        height: 34px;
                        z-index: 9;
                    }
                }
            }
        }
    }

    .accordion {
        gap: 20px;

        &-item {
            border: 0;
            background: transparent;
        }

        &-button {
            font-size: 20px;
            font-weight: 700;
            color: $black-color;
            padding: 24px 50px;
            background: $white-color;
            box-shadow: none;
            border-radius: 10px 10px 0 0 !important;

            &::after {
                background-image: url(../../assets/images/right-arrow.svg);
                background-size: 30px;
                transform: rotate(90deg);
                width: 30px;
                height: 30px;
                transition: 0.3s ease-in-out;
            }

            &:not(.collapsed) {
                &::after {
                    transform: rotate(-90deg);
                }
            }

            &.collapsed {
                border-radius: 10px !important;
            }
        }

        &-collapse {
            margin-top: 1px;
            background: $white-color;
            border-radius: 0 0 10px 10px;
        }

        &-body {
            padding: 50px;

            .form-input-group {
                gap: 30px;
            }

            .form-input {
                width: calc(50% - 15px);
            }

            .form-check {
                padding: 18px 40px 18px 60px;
                border-radius: 8px;
                border: 1px solid $light-gray-color;
                margin-bottom: 0;
            }

            .selected-interests-tiles {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                .interest-tile {
                    display: inline-flex;
                    align-items: center;
                    gap: 6px;
                    padding: 6px 12px;
                    background-color: $link-color;
                    color: $white-color;
                    border-radius: 20px;
                    font-size: 14px;
                    font-weight: 500;

                    .remove-interest {
                        background: none;
                        border: none;
                        color: $white-color;
                        font-size: 18px;
                        line-height: 1;
                        cursor: pointer;
                        padding: 0;
                        margin-left: 4px;
                        width: 16px;
                        height: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        transition: background-color 0.2s ease;

                        &:hover {
                            background-color: rgba(255, 255, 255, 0.2);
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: '1599px') {
    .profile-management {
        padding: 50px 0;
    }
}

@media (max-width: '1399px') {
    .profile-management {
        padding: 30px 0;

        .profile-box {
            padding: 40px;

            h5 {
                font-size: 18px;
            }
        }

        .accordion {
            &-button {
                padding: 20px 40px;
                font-size: 18px;
            }

            &-body {
                padding: 40px;

                .form-input-group {
                    gap: 20px;
                }
            }
        }
    }
}

@media (max-width: '767px') {
    .profile-management {
        min-height: calc(100dvh - 56px);
        padding: 30px 0;

        .profile-box {
            padding: 30px;

            h5 {
                font-size: 18px;
            }

            .profile-upload {
                .circle {
                    width: 130px;
                    height: 130px;
                }
            }
        }

        .accordion {
            gap: 16px;

            &-button {
                padding: 16px 30px;
                font-size: 16px;

                &::after {
                    width: 24px;
                    height: 24px;
                    background-size: 24px;
                }
            }

            &-body {
                padding: 30px;

                .form-input {
                    width: 100%;
                }

                .form-check {
                    padding: 13px 30px 13px 50px;
                }
            }
        }
    }
}