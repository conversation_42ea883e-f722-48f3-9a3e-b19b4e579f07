@use '@/variables' as *;

.search-bar-wrapper {
    border-radius: 10px;
    background: $white-color;
    padding: 12px;
    border: 1px solid $light-gray-color;
    box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.05);

    .form-input-group {
        gap: 14px;
        width: 100%;

        .form-input {
            .form-control {
                color: $black-color;
                font-size: 14px;
                font-weight: 500;
                line-height: normal;

                &::placeholder {
                    color: $black-color;
                    opacity: 1;
                }
            }

            .select__placeholder {
                color: $black-color;
            }

            .select__dropdown-indicator {
                svg {
                    color: $black-color;
                }
            }

            .select {
                &__control {
                    padding: 12px 16px;
                    width: 170px;
                    color: $black-color;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: normal;
                    height: 52px;
                }
            }

            .custom-dropdown {
                padding: 12px 16px;
                width: 170px;
                color: $black-color;
                font-size: 14px;
                font-weight: 500;
                line-height: normal;
                height: 52px;
                background: transparent;
                border: 1px solid #E7E7E7;
                border: 1px solid #E7E7E7;
                display: flex;
                align-items: center;
                justify-content: space-between;
                min-width: auto;


            }

            .dropdown-toggle::after {
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z'%3E%3C/path%3E%3C/svg%3E");
                background-size: 24px;
                width: 24px;
                height: 24px;
                margin-left: 0px;
                border: none;
            }

            .dropdown-menu {
                border-radius: 8px;
                background: $white-color;
                box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.10);
                padding: 16px;
                border: none;
                width: 170px;
                display: flex;
                flex-direction: column;
                gap: 10px;

                .dropdown-item {
                    padding: 0px;
                    background-color: transparent;

                    .form-check-label {
                        color: $black-color;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: normal;
                        margin-top: 3px;
                    }

                    &:active {
                        background-color: transparent;
                    }
                }
            }
        }

        .search-input {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px 16px;
            align-items: center;
            gap: 10px;
            display: flex;
            width: 100%;
            max-width: 270px;
            height: 52px;

            &.form-input {
                svg {
                    position: unset;
                    width: 17px;
                    height: 17px;

                }
            }

        }

        .img-filter {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px 16px;
            color: $black-color;
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            height: 52px;
            min-width: 170px;

            .custom-checkbox {
                .form-check-label {
                    margin-top: 3px;
                    color: $black-color !important;
                }
            }
        }

        .filter-btn {
            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 12px 16px;
            color: $black-color;
            font-size: 14px;
            font-weight: 500;
            line-height: normal;
            min-width: 170px;
            height: 52px;
        }

    }

    .search-btn {
        color: $white-color;
        min-width: auto;
        padding: 17px 30px;
    }


    .clear-filters {
        .search-result {
            border-radius: 8px;
            background: $light-yellow;
            display: flex;
            padding: 6px 10px 6px 6px;
            align-items: center;
            gap: 10px;
            width: fit-content;

            .close-btn {
                line-height: 1;
                width: 16px;
                height: 16px;
                object-fit: contain;
            }
        }


    }

    .clear-filter-btn {
        background: transparent;
        border: none;
        padding: 0px;
        color: #FF0101;
        font-size: 14px;
        font-weight: 700;
        line-height: normal;
        min-width: auto;
    }

    .custom-dropdown {
        padding: 12px 16px;
        width: 170px;
        color: $black-color;
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        height: 52px;
        background: transparent;
        border: 1px solid #E7E7E7;
        border: 1px solid #E7E7E7;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: auto;
    }

    .custom-interest-menu {
        width: 100%;
        min-width: 800px;

        &-data {
            max-height: 400px;
            overflow-y: auto;
        }


        .interest-card {
            width: 102px;
            height: 102px;
            min-width: 102px;
            border-radius: 4px;
            cursor: pointer;

            &::before {
                content: "";
                position: absolute;
                border-radius: 4px;
                background: rgba(0, 0, 0, 0.20);
                left: 0px;
                top: 0px;
                width: 102px;
                height: 102px;
                 min-width: 102px;
            }

            .label-overlay {
                position: absolute;
                bottom: 10px;
                left: 10px;
                color: $white-color;
                font-size: 13px;
                font-weight: 400;
                line-height: normal;
            }

            .checkmark {
                position: absolute;
                top: 6px;
                right: 6px;
                width: 18px;
                height: 18px;
                border: 2px solid #fff;
                border-radius: 50%;
                background-color: transparent;
                z-index: 2;

                &::after {
                    content: "";
                    display: block;
                    width: 12px;
                    height: 12px;
                    margin: 1px auto;
                    border-radius: 50%;
                    background-color: #b936ad;
                    opacity: 0;
                    transition: 0.2s;
                }
            }

            &.selected {
                .checkmark {
                    border-color: #b936ad;
                    background-color: #fff;

                    &::after {
                        opacity: 1;
                    }
                }
            }

        }

    }

}

@media(max-width:575px) {
    .search-bar-wrapper {
        .search-filters {
            flex-direction: column;

            .form-input-group {
                .search-input {
                    max-width: 100%;
                }

                .form-input {
                    width: 100%;

                    .custom-dropdown {
                        width: 100%;
                    }
                }

                .img-filter {
                    min-width: 100%;
                }

                .filter-btn {
                    min-width: 100%;
                }

                .custom-interest-menu {
                    min-width: 300px;
                    &-data{
                        .flex-1{
                            flex: 1;
                        }
                    }
                    .interest-card {
                        width: 90px;
                        height: 90px;
                         min-width: 90px;

                        &::before {
                            width: 90px;
                            height: 90px;
                            min-width: 90px;
                        }
                    }
                }
            }
        }

        .filter-result-dispaly {
            flex-direction: column;
        }

        .clear-filter-btn {
            text-align: left;
        }

        .search-btn {
            min-width: 100%;
            justify-content: center;
        }
    }
}
@media(min-width:576px) and (max-width:991px){
     .search-bar-wrapper {
        .search-filters {
            flex-direction: column;

            .form-input-group {
                .search-input {
                    max-width: 100%;
                }

                .form-input {
                    width: 48%;

                    .custom-dropdown {
                        width: 100%;
                    }
                }

                .img-filter {
                    min-width: 48%;
                }

                .filter-btn {
                    min-width: 48%;
                }

                .custom-interest-menu {
                    min-width: 400px;
                    &-data{
                        .flex-1{
                            flex: 1;
                        }
                    }
                    
                }
            }
        }

        .filter-result-dispaly {
            flex-direction: column;
        }

        .clear-filter-btn {
            text-align: left;
        }

        .search-btn {
            min-width: 100%;
            justify-content: center;
        }
    }
}