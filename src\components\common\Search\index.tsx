import { useMaster } from "@/api/utils.api";
import AdvancedFilterPanel from "@/components/advancedfilterpanel/AdvancedFilterPanel";
import { useTranslation } from "@/hooks/useTranslation";
import { IMAGE_PATHS } from "@/utils/image-path";
import { SearchNormal1 } from "iconsax-react";
import { useMemo, useState } from "react";
import { Button, Form, Image } from "react-bootstrap";
import CustomDropdown from "../CustomDropdown";
import FilterByInterest from "./FilterByInterest";
import FilterByName from "./FilterByName";
import FilterTags from "./FilterTags";
import "./styles.scss";

const ageOptions = [
  { value: "18-20", label: "18–20" },
  { value: "20-30", label: "20–30" },
  { value: "30-40", label: "30–40" },
  { value: "40-50", label: "40–50" },
  { value: "50+", label: "50+" },
];

const distanceOptions = [
  { value: "50km", label: "50 km" },
  { value: "100km", label: "100 km" },
  { value: "200km", label: "200 km" },
  { value: "500km", label: "500 km" },
  { value: "any", label: "Any" },
];

const SearchBar = ({
  filters = {},
  setFilters,
  onClearFilters,
  onSearchWithFilters,
  setParams,
}: any) => {
  const { t } = useTranslation();
  const { data: master = {} } = useMaster();

  const interestsOptions = useMemo(
    () =>
      (master.interest || []).map((item: any) => ({
        value: item.id,
        label: item.title,
        image: item.image,
      })),
    [master.interest]
  );

  const [showFilters, setShowFilters] = useState(false);
  const [selectedInterests, setSelectedInterests] = useState<any[]>(
    filters?.interestIds || []
  );

  const onChangeFilter = (key: string, value: any) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  const getSelectedOptionValues = (key: string) => {
    return Array.isArray(filters?.[key])
      ? filters[key].map((o: any) => o.value)
      : [];
  };

  return (
    <>
      <div className="search-bar-wrapper d-flex flex-column gap-3 ">
        <div className="d-flex justify-content-between gap-3 align-items-start search-filters">
          <div className="form-input-group d-flex align-items-center flex-wrap">
            <FilterByName onChangeFilter={onChangeFilter} value={filters?.name || ""} />

            <CustomDropdown
              title={t('search.ageRange')}
              options={ageOptions}
              selectedOptions={getSelectedOptionValues("ageRange")}
              onSelect={(selected) =>
                onChangeFilter(
                  "ageRange",
                  ageOptions.filter((opt) => selected.includes(opt.value))
                )
              }
              multiSelect={true}
            />

            <CustomDropdown
              title={t('search.distance')}
              options={distanceOptions}
              selectedOptions={getSelectedOptionValues("distance")}
              onSelect={(selected) =>
                onChangeFilter(
                  "distance",
                  distanceOptions.filter((opt) => selected.includes(opt.value))
                )
              }
              multiSelect={true}
            />

            <FilterByInterest
              interestData={interestsOptions}
              selected={selectedInterests}
              setSelected={setSelectedInterests}
              toggleSelect={(item: any) => {
                const exists = selectedInterests.find(
                  (i: any) => i.value === item.value
                );
                if (exists) {
                  setSelectedInterests(
                    selectedInterests.filter((i: any) => i.value !== item.value)
                  );
                } else {
                  setSelectedInterests([...selectedInterests, item]);
                }
              }}
              onSubmit={(values: any[]) =>
                onChangeFilter("interestIds", values)
              }
            />

            <div className="img-filter">
              <Form.Check
                type="checkbox"
                id="with-photos"
                label={t('search.withPhotos')}
                className="custom-checkbox"
                defaultChecked
                checked={filters?.withPhotos}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  onChangeFilter("withPhotos", e.target.checked)
                }
              />
            </div>

            <Button
              variant="outline-secondary"
              className=" d-flex align-items-center gap-2 filter-btn"
              onClick={() => setShowFilters(true)}
            >
              <span>{t('search.advancedFilter')}</span>
              <Image src={IMAGE_PATHS.FilterIcon} alt="" />
            </Button>
          </div>
          <Button
            variant="gradient-purple"
            className="d-flex align-items-center gap-2 search-btn"
            onClick={onSearchWithFilters}
          >
            <SearchNormal1 size="16" color="#ffffff" />
            {t('search.search')}
          </Button>
        </div>
        <div className="d-flex clear-filters justify-content-between gap-3 filter-result-dispaly">
          <div className="d-flex aling-items-center gap-2 flex-wrap">
            <FilterTags filters={filters} setFilters={setFilters} />
          </div>
          <Button className="clear-filter-btn" onClick={onClearFilters}>
            {t('search.clearFilter')}
          </Button>
        </div>
      </div>

      <AdvancedFilterPanel
        show={showFilters}
        onClose={() => setShowFilters(false)}
        master={master}
        filters={filters}
        setFilters={setFilters}
        setParams={setParams}
      />
    </>
  );
};

export default SearchBar;
