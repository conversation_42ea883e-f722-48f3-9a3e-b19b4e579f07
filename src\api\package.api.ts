import { API_ENDPOINTS } from "@/globals";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "./apiClient";

export const usePackages = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_PACKAGES);
            return response;
        },
        queryKey: ["packages"],
    });

export const usePurchasePackageMutation = () => {
    return useMutation({
        mutationFn: (payload: Record<string, any>) => {
            return apiClient.post(API_ENDPOINTS.PURCHASE_PACKAGE, payload);
        },
    });
};

export const useActivePackage = () =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.GET_ACTIVE_PACKAGE);
            return response;
        },
        queryKey: ["active-package"],
    });