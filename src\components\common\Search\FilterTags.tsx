import { FormatS3ImgUrl } from "@/utils";
import { IMAGE_PATHS } from "@/utils/image-path";
import { Image } from "react-bootstrap";

interface FilterTagsProps {
  filters: any;
  setFilters: (filters: any) => void;
}

const SHOW_FILTERS_KEY = ["ageRange", "distance", "interestIds"];

const FilterTags = ({ filters, setFilters }: FilterTagsProps) => {
  const filterKeys = Object.keys(filters).filter(
    (key) =>
      SHOW_FILTERS_KEY.includes(key) &&
      ((Array.isArray(filters[key]) && filters[key].length > 0) ||
        (filters[key] &&
          typeof filters[key] === "object" &&
          !Array.isArray(filters[key])))
  );

  const handleRemove = (filterKey: string, item: any) => {
    if (Array.isArray(filters[filterKey])) {
      const updated = filters[filterKey].filter(
        (v: any) => v.value !== item.value || v.id !== item.id
      );
      setFilters({
        ...filters,
        [filterKey]: updated,
      });
    } else {
      setFilters({
        ...filters,
        [filterKey]: undefined,
      });
    }
  };

  return (
    <>
      {filterKeys.map((filterKey) => {
        const value = filters[filterKey];
        if (Array.isArray(value)) {
          return value.map((v: any, index: number) => (
            <div className="search-result" key={`${filterKey}-${index}`}>
              {v?.image && (
                <Image
                  src={FormatS3ImgUrl(v?.image)}
                  roundedCircle
                  className="interest-img object-fit-cover"
                  width={30}
                  height={30}
                />
              )}
              {v?.label || v?.title || v.value || v}
              <button
                className="bg-transparent border-0 p-0 close-btn"
                onClick={() => handleRemove(filterKey, v)}
              >
                <Image src={IMAGE_PATHS.CloseIcon} alt="Remove" />
              </button>
            </div>
          ));
        } else if (value && typeof value === "object") {
          return (
            <div className="search-result" key={filterKey}>
              {value?.image && (
                <Image
                  src={FormatS3ImgUrl(value?.image)}
                  roundedCircle
                  className="interest-img object-fit-cover"
                  width={30}
                  height={30}
                />
              )}
              {value?.label || value?.title || value.value || value}
              <button
                className="bg-transparent border-0 p-0 close-btn"
                onClick={() => handleRemove(filterKey, value)}
              >
                <Image src={IMAGE_PATHS.CloseIcon} alt="Remove" />
              </button>
            </div>
          );
        }
        return null;
      })}
    </>
  );
};

export default FilterTags;
