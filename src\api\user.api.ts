import { API_ENDPOINTS } from "@/globals";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "./apiClient";

export const useMyProfile = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.MY_PROFILE);
      return response || {};
    },
    queryKey: ["my-profile"],
  });

export const useUpdateProfileMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.put(API_ENDPOINTS.UPDATE_PROFILE, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["my-profile"] });
    },
  });
};

export const useUpdateAvatarMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.put(API_ENDPOINTS.UPDATE_AVATAR, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["my-profile"] });
    },
  });
};

export const useUpdateNotificationsMutation = () => {
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.put(API_ENDPOINTS.UPDATE_NOTIFICATIONS, payload);
    },
  });
};

export const useUploadAlbumMutation = () => {
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.put(API_ENDPOINTS.UPLOAD_ALBUM, payload);
    },
  });
};

export const useDeleteImageMutation = () => {
  return useMutation({
    mutationFn: (id: string) => {
      return apiClient.delete(API_ENDPOINTS.DELETE_IMAGE(id));
    },
  });
};
