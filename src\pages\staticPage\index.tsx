import React, { useLayoutEffect, useRef } from "react";
import { Spinner } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import { useStaticPage } from "@/api/utils.api";
import parse from "html-react-parser";
import "./styles.scss";

const StaticPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { data: pageData, isLoading, error } = useStaticPage(slug || "");

  const translation = pageData?.translation;

  const scrollRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (!isLoading && translation?.description && scrollRef.current) {
      scrollRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [slug, isLoading, translation?.description]);

  const handleGoBack = () => navigate(-1);

  const renderLayout = (children: React.ReactNode) => (
    <div
      className="static-page-container inner-height"
      key={slug}
      ref={scrollRef}
    >
      {/* <button
        onClick={handleGoBack}
        className="back-button position-absolute text-light fw-bold"
      >
        Back
      </button> */}
      <div
        ref={scrollRef}
        className="d-flex flex-column justify-content-center align-items-center h-100"
      >
        {children}
      </div>
    </div>
  );

  if (isLoading) {
    return renderLayout(
      <>
        <Spinner animation="border" role="status" className="mb-3">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p>Loading page content...</p>
      </>
    );
  }

  if (error || !translation) {
    return renderLayout(
      <div className="text-center">
        <h3 className="text-danger mb-3">Page Not Found</h3>
        <p className="mb-4">The requested page could not be found.</p>
      </div>
    );
  }

  return (
    <div className="static-page-container" key={slug} ref={scrollRef}>
      {/* <button
        onClick={handleGoBack}
        className="back-button position-absolute text-light fw-bold"
      >
        Back
      </button> */}

      <div className="d-flex flex-column gap-4 h-100">
        <div className="static-page-heading text-center">
          <h3 className="color-primary fw-bold fs-1">
            <span>{translation.title}</span>
          </h3>
        </div>

        <div className="static-page-content">
          <div className="content-wrapper">
            {parse(translation.description)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StaticPage;
