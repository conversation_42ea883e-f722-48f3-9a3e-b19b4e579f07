import React, { useState } from 'react';
import './styles.scss';
import { useTranslation } from '@/hooks/useTranslation';
import { Button, Container, Form, InputGroup } from 'react-bootstrap';
import { ArrowLeft2, AttachCircle, Gift, SearchNormal1, Send, Smileys } from 'iconsax-react';
import { IMAGE_PATHS } from '@/utils/image-path';



const Messages: React.FC = () => {
    const { t } = useTranslation();
    const [activeChat, setActiveChat] = useState<number | null>(null);
    const isMobile = window.innerWidth <= 768; // optional, you can enhance with useEffect

    return (
        <>
            <Container fluid>
                <div className='messages'>
                    {isMobile && activeChat !== null && (
                        <Button
                            className="back-btn bg-transparent border-0 p-0 z-3 mb-3"
                            onClick={() => setActiveChat(null)}
                        >
                           <ArrowLeft2 size="16" color="#141414"/> {t('messages.back')}
                        </Button>
                    )}

                    <div className="chat-area d-flex">

                        {/* Sidebar */}
                        <div className={`sidebar d-flex flex-column ${isMobile && activeChat !== null ? 'd-none' : ''}`}>
                            <div className="people-search-bar position-relative mb-2">
                                <SearchNormal1 size="24" color="#141414" />
                                <Form.Control type="text" placeholder={t('messages.searchPeople')} className="rounded-pill" />
                            </div>
                            <div className="people-list">
                                {Array(8).fill(0).map((_, index) => (
                                    <div key={index} className={`person d-flex ${index === activeChat ? 'active' : ''}`}
                                        onClick={() => setActiveChat(index)}>
                                        <img src={IMAGE_PATHS.memberList7} alt="avatar" className='rounded-circle' />
                                        <div style={{ flex: 1 }}>
                                            <div className='d-flex align-items-center justify-content-between'>
                                                <h6 className="mb-0 fw-semibold">Smile-up, 23</h6>
                                                <div className="time">5 min ago</div>
                                            </div>
                                            <p className="preview mb-0">Good morning. How are you? Can we meet yesterday?...</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Chat Window */}

                        <div className={`chat-window d-flex flex-column ${isMobile && activeChat === null ? 'd-none' : ''}`}>
                            <div className="chat-header d-flex align-items-center">
                                <img src={IMAGE_PATHS.memberList2} alt="avatar" className='rounded-circle' />
                                <div>
                                    <h5 className="name mb-0">Smile-up, 23</h5>
                                    <p className="location mb-0">Bridlepath, Canada</p>
                                </div>
                            </div>
                            <div className="chat-messages">
                                <div className="message d-flex received">
                                    <img src={IMAGE_PATHS.memberList2} alt="avatar" className='rounded-circle' />
                                    <div className='message-content'>
                                        <div className="bubble">next time you'll be awake at this hour why not now</div>
                                        <div className="bubble">Didn't I tell you not to put your phone on charge just because it's the weekend? 🥰😘😍❤️</div>
                                        <div className="timestamp">Sat 5:10 AM</div>
                                    </div>
                                </div>
                                <div className="message d-flex sent">
                                    <div className='message-content'>
                                        <div className="bubble">next time you'll be awake at this hour why not now</div>
                                        <div className="bubble">🥰😘😍❤️</div>
                                        <div className="timestamp">Sat 5:10 AM</div>
                                    </div>
                                    <img src={IMAGE_PATHS.memberList2} alt="avatar" className='rounded-circle' />
                                </div>

                                <div className="date-divider d-flex align-items-center">
                                    <hr />
                                    <div>Tuesday Dec 2, 2024</div>
                                    <hr />
                                </div>

                                <div className="message d-flex received">
                                    <img src={IMAGE_PATHS.memberList2} alt="avatar" className='rounded-circle' />
                                    <div className='message-content'>
                                        <div className="bubble">next time you'll be awake at this hour why not now</div>
                                        <div className="bubble">Didn't I tell you not to put your phone on charge just because it's the weekend?</div>
                                        <div className="timestamp">Sat 5:10 AM</div>
                                    </div>
                                </div>
                            </div>
                            <div className="chat-input">
                                <InputGroup>
                                    <Form.Control placeholder={t('messages.enterMessage')} className='border-0' />
                                    <Button className='p-0 border-0 bg-transparent'><AttachCircle size="24" color="#141414" /></Button>
                                    <Button className='p-0 border-0 bg-transparent'><Gift size="24" color="#141414" /></Button>
                                    <Button className='p-0 border-0 bg-transparent'><Smileys size="24" color="#141414" /></Button>
                                    <Button className='send-btn p-0 border-0 rounded-circle ms-2'><Send size="18" color="#FFF" variant="Bold" /></Button>
                                </InputGroup>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        </>
    );
};

export default Messages;
