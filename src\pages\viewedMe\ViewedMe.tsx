import React from 'react'
import MembersCard from "@/components/memberscard/MembersCard"
import { useTranslation } from '@/hooks/useTranslation';
import { Button, Container } from 'react-bootstrap';
import './styles.scss'
import { IMAGE_PATHS } from '@/utils/image-path'

const ViewedMe: React.FC = () => {
    const { t } = useTranslation();
    return (
        <div className='viewed-page'>
            <Container fluid>
                <div className='d-flex flex-column gap-3'>
                    <div className='d-flex aling-items-center gap-3'>
                        <Button className='first-btn'>
                            {t('viewedMe.myViews', { count: 10 })}
                        </Button>
                        <Button className='second-btn'>
                            {t('viewedMe.viewedMe', { count: 34 })}
                        </Button>
                    </div>
                    <div className="d-flex flex-wrap members-list-content ">
                        <MembersCard image={IMAGE_PATHS.memberList1} />
                        <MembersCard image={IMAGE_PATHS.memberList2} />
                        <MembersCard image={IMAGE_PATHS.memberList3} />
                        <MembersCard image={IMAGE_PATHS.memberList4} />
                        <MembersCard image={IMAGE_PATHS.memberList5} />
                        <MembersCard image={IMAGE_PATHS.memberList6} />
                        <MembersCard image={IMAGE_PATHS.memberList7} />
                        <MembersCard image={IMAGE_PATHS.memberList8} />
                        <MembersCard image={IMAGE_PATHS.memberList9} />
                        <MembersCard image={IMAGE_PATHS.memberList1} />
                        <MembersCard image={IMAGE_PATHS.memberList2} />
                        <MembersCard image={IMAGE_PATHS.memberList3} />
                        <MembersCard image={IMAGE_PATHS.memberList4} />
                        <MembersCard image={IMAGE_PATHS.memberList5} />
                        <MembersCard image={IMAGE_PATHS.memberList6} />
                        <MembersCard image={IMAGE_PATHS.memberList7} />
                        <MembersCard image={IMAGE_PATHS.memberList8} />
                        <MembersCard image={IMAGE_PATHS.memberList9} />
                    </div>
                </div>
            </Container>
        </div>
    )
}

export default ViewedMe