import { useFlirtMessages } from "@/api";
import React from "react";
import { Offcanvas, Spinner } from "react-bootstrap";
import "./styles.scss";

interface FlirtMessage {
  id: number;
  message: string;
  languageCode: string;
}

const FlirtPanel: React.FC<any> = ({ show, onHide }: any) => {
  const { data: { data: { flirtMessage = [] } = {} } = {}, isLoading } =
    useFlirtMessages();

  const handleClose = () => {
    onHide();
  };

  return (
    <>
      <Offcanvas
        show={show}
        onHide={handleClose}
        placement="end"
        className="flirt-offcanvas"
      >
        <Offcanvas.Header closeButton>
          <Offcanvas.Title>Flirt with Me</Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body className="d-flex flex-column gap-3">
          {isLoading ? (
            <div className="d-flex justify-content-center align-items-center h-100">
              <Spinner />
            </div>
          ) : (
            <>
              {flirtMessage.map((msg: FlirtMessage) => (
                <div key={msg?.id} className="flirt-message rounded-3 p-3 ">
                  {msg?.message}
                </div>
              ))}
            </>
          )}
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};

export default FlirtPanel;
