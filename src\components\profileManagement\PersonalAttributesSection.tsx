import React from "react";
import { Accordion, Form } from "react-bootstrap";
import MemoizedSelect from "./MemoizedSelect";

interface PersonalAttributesSectionProps {
  formik: any;
  handleSelectChange: (field: string) => (option: any, isMulti?: boolean) => void;
  personalityOptions: Array<{ value: any; label: string }>;
  ethnicityOptions: Array<{ value: any; label: string }>;
  religionOptions: Array<{ value: any; label: string }>;
  interestsOptions: Array<{ value: any; label: string }>;
  isMasterLoading: boolean;
}

const PersonalAttributesSection: React.FC<PersonalAttributesSectionProps> = ({
  formik,
  handleSelectChange,
  personalityOptions,
  ethnicityOptions,
  // religionOptions,
  interestsOptions,
  isMasterLoading,
}) => {
  const handleInterestChange = (selectedOptions: any) => {
    console.log('Interest change - selectedOptions:', selectedOptions);
    const values = selectedOptions ? selectedOptions.map((option: any) => option.value) : [];
    console.log('Interest change - values:', values);
    formik.setFieldValue('interest', values);
  };

  return (
  <Accordion.Item eventKey="3">
    <Accordion.Header>Personal Attributes</Accordion.Header>
    <Accordion.Body>
      <div className="form-input-group d-flex flex-wrap">
        <Form.Group className="form-input">
          <Form.Label>Personality</Form.Label>
          <MemoizedSelect
            isSearchable
            options={personalityOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Personality"}
            classNamePrefix="select"
            name="personality"
            value={formik.values.personality}
            onChange={handleSelectChange("personality")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Ethnicity</Form.Label>
          <MemoizedSelect
            isSearchable
            options={ethnicityOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Ethnicity"}
            classNamePrefix="select"
            name="ethnicity"
            value={formik.values.ethnicity}
            onChange={handleSelectChange("ethnicity")}
          />
        </Form.Group>
        {/* <Form.Group className="form-input">
          <Form.Label>Religion</Form.Label>
          <MemoizedSelect
            isSearchable
            options={religionOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Religion"}
            classNamePrefix="select"
            name="religion"
            value={formik.values.religion}
            onChange={handleSelectChange("religion")}
          />
        </Form.Group> */}
        <Form.Group className="form-input">
          <Form.Label>Interests</Form.Label>
          <MemoizedSelect
            isSearchable
            options={interestsOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Interests"}
            classNamePrefix="select"
            name="interest"
            value={
              Array.isArray(formik.values.interest)
                ? interestsOptions.filter((o: any) =>
                    formik.values.interest.includes(o.value)
                  )
                : []
            }
            onChange={handleInterestChange}
            isMulti={true}
          />
          {/* Display selected interests as tiles */}
          {Array.isArray(formik.values.interest) && formik.values.interest.length > 0 && (
            <div className="selected-interests-tiles mt-2">
              {formik.values.interest.map((interestId: any) => {
                const interest = interestsOptions.find((opt: any) => opt.value === interestId);
                return interest ? (
                  <span key={interestId} className="interest-tile">
                    {interest.label}
                    <button
                      type="button"
                      className="remove-interest"
                      onClick={() => {
                        const newInterests = formik.values.interest.filter((id: any) => id !== interestId);
                        formik.setFieldValue('interest', newInterests);
                      }}
                    >
                      ×
                    </button>
                  </span>
                ) : null;
              })}
            </div>
          )}
        </Form.Group>
      </div>
    </Accordion.Body>
  </Accordion.Item>
);
};

export default PersonalAttributesSection;
